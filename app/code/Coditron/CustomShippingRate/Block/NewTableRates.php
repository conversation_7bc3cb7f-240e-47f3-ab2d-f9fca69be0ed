<?php
/**
 * Webkul Software.
 *
 * @category  Webkul
 * @package   Webkul_MpSellerCategory
 * <AUTHOR> Software Private Limited
 * @copyright Webkul Software Private Limited (https://webkul.com)
 * @license   https://store.webkul.com/license.html
 */
namespace Coditron\CustomShippingRate\Block;

use Magento\Framework\View\Element\Template;
use Magento\Framework\View\Element\Template\Context;

class NewTableRates extends Template
{
    /**
     * @var \Webkul\Marketplace\Helper\Data
     */
    protected $mpHelper;

    /**
     * @var \Coditron\CustomShippingRate\Helper\Data
     */
    protected $helper;

    /**
     * Constructor
     *
     * @param Context $context
     * @param \Webkul\Marketplace\Helper\Data $mpHelper
     * @param \Coditron\CustomShippingRate\Helper\Data $helper
     * @param array $data
     */
    public function __construct(
        Context $context,
        \Webkul\Marketplace\Helper\Data $mpHelper,
        \Coditron\CustomShippingRate\Helper\Data $helper,
        array $data = []
    ) {
        $this->mpHelper = $mpHelper;
        $this->helper = $helper;
        parent::__construct($context, $data);
    }

    /**
     * Get marketplace helper
     *
     * @return \Webkul\Marketplace\Helper\Data
     */
    public function getMpHelper()
    {
        return $this->mpHelper;
    }

    /**
     * Get seller ID
     *
     * @return int
     */
    public function getSellerId()
    {
        return $this->helper->getSellerId();
    }
    
    /**
     * Get custom title for new shipping methods tab
     * 
     * @return string
     */
    public function getTabTitle()
    {
        return __('New Shipping Methods');
    }
    
    /**
     * Get custom description for new shipping methods
     * 
     * @return string
     */
    public function getTabDescription()
    {
        return __('Manage your new shipping methods and rates');
    }
    
    /**
     * Check if this is the new shipping methods tab
     * 
     * @return bool
     */
    public function isNewShippingTab()
    {
        return true;
    }
    
    /**
     * Get custom CSS classes for new tab
     * 
     * @return string
     */
    public function getCustomCssClasses()
    {
        return 'wk-mp-new-shipping-methods';
    }
    
    /**
     * You can override any method from the parent TableRates class here
     * to customize behavior for the second tab
     */
    
    /**
     * Example: Get filtered shipping rates for new tab
     * You can customize this to show different data
     * 
     * @return \Coditron\CustomShippingRate\Model\ResourceModel\ShipTableRates\Collection
     */
    public function getFilteredShippingRates()
    {
        // Example: Get only recent shipping rates
        $collection = $this->_shipTableRatesFactory->create()->getCollection();
        $collection->addFieldToFilter('seller_id', $this->getSellerId());
        
        // Add custom filters here
        // $collection->addFieldToFilter('created_at', ['gteq' => date('Y-m-d', strtotime('-30 days'))]);
        // $collection->addFieldToFilter('service_type', ['in' => ['express', 'priority']]);
        
        return $collection;
    }
}
